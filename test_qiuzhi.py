#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# 模拟测试求知插件的功能
import sys
import json

# 模拟API返回的数据
test_data = {
    "time": "2024-01-01 12:00:00",
    "title": "测试标题：神秘的百慕大三角",
    "content": "百慕大三角是一个充满神秘色彩的海域。\\n在这片海域中，曾经发生过许多令人费解的事件。\\n飞机和船只在这里神秘失踪，至今仍是未解之谜。",
    "img_url": ["https://example.com/image.jpg"],
    "author": "测试作者"
}

# 模拟sender类
class MockSender:
    def __init__(self):
        self.messages = []
        self.images = []
    
    def getMessage(self):
        return "1"
    
    def reply(self, msg):
        self.messages.append(msg)
        print(f"📤 回复消息: {msg}")
    
    def sendImage(self, url):
        self.images.append(url)
        print(f"📷 发送图片: {url}")
        # 模拟发送成功
        return True

# 模拟middleware
class MockMiddleware:
    def bucketGet(self, bucket, key):
        if bucket == "qiuzhi" and key == "api_url":
            return "http://api.yujn.cn/api/wjzm.php"
        return None
    
    def bucketSet(self, bucket, key, value):
        pass
    
    def getSenderID(self):
        return "test_user_123"

# 设置模拟环境
import importlib.util
import html

# 导入求知模块进行测试
def test_format_content():
    """测试format_content函数"""
    print("🧪 开始测试format_content函数...")
    
    # 模拟处理函数
    def format_content(data):
        """格式化返回内容"""
        try:
            if not data:
                return "❌ 未获取到内容"
            
            # 提取需要的字段
            time_str = data.get("time", "")
            title = data.get("title", "")
            content = data.get("content", "")
            img_url = data.get("img_url", [])
            
            # 处理内容中的换行符和HTML实体编码
            if content:
                # 替换换行符为制表符
                content = content.replace("\\n", "\t").replace("\n", "\t")
                
                # 使用html.unescape处理HTML实体编码
                content = html.unescape(content)
                
                # 格式化内容，添加段落分隔，美化阅读
                # 将制表符替换为换行，每60个字符左右换行
                content_lines = []
                sentences = content.split('\t')
                
                for sentence in sentences:
                    if len(sentence.strip()) > 0:
                        # 每60个字符左右换行
                        while len(sentence) > 60:
                            # 找到合适的断句点
                            break_point = 60
                            for i in range(50, min(70, len(sentence))):
                                if sentence[i] in '，。！？；：':
                                    break_point = i + 1
                                    break
                            content_lines.append(sentence[:break_point])
                            sentence = sentence[break_point:]
                        if sentence.strip():
                            content_lines.append(sentence)
                
                content = '\n'.join(content_lines)
            
            # 构建结果部分数组
            result_parts = []
            
            if time_str:
                result_parts.append(f"📅 时间: {time_str}")
            
            if title:
                result_parts.append(f"📖 标题: {title}")
            
            if content:
                result_parts.append(f"📝 内容:\n{content}")
            
            # 处理图片
            sender = MockSender()
            image_sent = False
            if img_url and isinstance(img_url, list) and len(img_url) > 0:
                try:
                    # 尝试发送图片
                    print(f"🔍 尝试发送图片: {img_url[0]}")
                    sender.sendImage(img_url[0])
                    print(f"✅ 图片发送成功: {img_url[0]}")
                    image_sent = True
                    result_parts.append("🖼️ 图片已发送")
                except Exception as e:
                    print(f"❌ 图片发送失败: {e}")
                    # 如果sendImage失败，将图片URL添加到结果中
                    result_parts.append(f"🖼️ 图片: {img_url[0]}")
            
            # 使用制表符分隔各部分
            formatted_text = "\t".join(result_parts)
            
            return formatted_text
            
        except Exception as e:
            print(f"❌ 格式化异常: {e}")
            return f"❌ 数据格式化失败: {str(data)}"
    
    # 测试
    result = format_content(test_data)
    print(f"🎯 格式化结果:")
    print(result)
    print()
    
    # 验证是否使用了制表符分隔
    parts = result.split('\t')
    print(f"✅ 使用制表符分隔，共{len(parts)}个部分:")
    for i, part in enumerate(parts, 1):
        print(f"  {i}. {part[:50]}...")
    
    return result

if __name__ == "__main__":
    test_format_content()