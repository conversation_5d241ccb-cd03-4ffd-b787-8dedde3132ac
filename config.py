#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
用户等级修改配置文件
请根据需要修改以下配置
"""

# API配置
API_CONFIG = {
    "base_url": "http://cdn.jusuanyun.vip",
    "token": "7e2820ff7d6273fbae54c23a21ea0be35613fc6bdc7ee6473672d6dace8f98fa"
}

# 用户配置
USER_CONFIG = {
    "user_id": 103,        # 目标用户ID
    "current_level": 2,    # 当前等级
    "target_level": 3      # 目标等级
}

# 等级说明
LEVEL_DESCRIPTIONS = {
    1: {
        "name": "V1（注册会员）",
        "commission": "0%",
        "channel_income": "0%",
        "requirement": "注册即可"
    },
    2: {
        "name": "V2会员",
        "commission": "10%",
        "channel_income": "10%",
        "requirement": "默认等级"
    },
    3: {
        "name": "V3会员", 
        "commission": "12%",
        "channel_income": "12%",
        "requirement": "累计业绩达到50万"
    },
    4: {
        "name": "V4会员",
        "commission": "14%", 
        "channel_income": "14%",
        "requirement": "累计业绩300万"
    },
    5: {
        "name": "V5会员",
        "commission": "15%",
        "channel_income": "15%", 
        "requirement": "累计业绩1000万"
    },
    6: {
        "name": "V6股东",
        "commission": "18%",
        "channel_income": "18%",
        "requirement": "累计业绩1500万"
    }
}

# HTTP请求配置
REQUEST_CONFIG = {
    "timeout": 30,
    "max_retries": 3,
    "retry_delay": 2
}

# 调试配置
DEBUG_CONFIG = {
    "enable_logging": True,
    "log_requests": True,
    "log_responses": True,
    "simulate_mode": False  # 设为True时使用模拟模式
}