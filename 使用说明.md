# 聚算云用户等级修改工具使用说明

## 📋 文件说明

1. **config.py** - 配置文件，包含API地址、Token、用户信息等
2. **run_level_update.py** - 简化执行脚本（推荐使用）
3. **update_user_level.py** - 完整功能脚本（包含更多调试信息）
4. **使用说明.md** - 本说明文件

## 🚀 快速开始

### 1. 修改配置文件

编辑 `config.py` 文件，修改以下参数：

```python
# API配置
API_CONFIG = {
    "base_url": "http://cdn.jusuanyun.vip",
    "token": "你的Token"  # 替换为实际Token
}

# 用户配置
USER_CONFIG = {
    "user_id": 103,        # 目标用户ID
    "current_level": 2,    # 当前等级
    "target_level": 3      # 目标等级
}
```

### 2. 运行脚本

在终端中执行：

```bash
cd "/Users/<USER>/Desktop/UGO 插件"
python3 run_level_update.py
```

## 📊 等级说明

| 等级 | 名称 | 推荐佣金 | 渠道收益 | 升级要求 |
|------|------|----------|----------|----------|
| V1 | 注册会员 | 0% | 0% | 注册即可 |
| V2 | V2会员 | 10% | 10% | 默认等级 |
| V3 | V3会员 | 12% | 12% | 累计业绩50万 |
| V4 | V4会员 | 14% | 14% | 累计业绩300万 |
| V5 | V5会员 | 15% | 15% | 累计业绩1000万 |
| V6 | V6股东 | 18% | 18% | 累计业绩1500万 |

## 🔧 高级配置

### 调试模式

在 `config.py` 中可以启用调试选项：

```python
DEBUG_CONFIG = {
    "enable_logging": True,    # 启用日志
    "log_requests": True,      # 记录请求
    "log_responses": True,     # 记录响应
    "simulate_mode": False     # 模拟模式（测试用）
}
```

### 模拟模式

如果要测试脚本而不实际修改数据，可以启用模拟模式：

```python
DEBUG_CONFIG = {
    "simulate_mode": True  # 启用模拟模式
}
```

## ⚠️ 注意事项

1. **Token有效性**: 确保Token未过期，当前Token: `7e2820ff7d6273fbae54c23a21ea0be35613fc6bdc7ee6473672d6dace8f98fa`

2. **权限要求**: 修改用户等级可能需要管理员权限

3. **业绩要求**: 系统可能会检查用户是否满足对应等级的业绩要求

4. **API变更**: 如果API端点发生变化，脚本会自动尝试多个可能的端点

## 🔍 故障排除

### 常见错误及解决方案

1. **Token错误 (code: -2)**
   - 检查Token是否正确
   - 确认Token未过期
   - 联系管理员获取新Token

2. **权限不足**
   - 确认当前用户有修改等级的权限
   - 可能需要使用管理员账户

3. **业绩不足**
   - 检查用户当前业绩是否达到目标等级要求
   - 可能需要先提升业绩

4. **网络连接问题**
   - 检查网络连接
   - 确认API地址可访问

## 📝 日志说明

脚本运行时会输出详细日志：

- 🔍 表示查询操作
- 🔄 表示尝试操作  
- ✅ 表示成功
- ❌ 表示失败
- ⚠️ 表示警告
- 📊 表示数据信息
- 🎉 表示完成

## 🤝 技术支持

如遇问题，请检查：

1. Python版本（建议3.7+）
2. 网络连接状态
3. Token有效性
4. 用户权限

需要帮助时，请提供完整的错误日志信息。