#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简化的用户等级修改执行脚本
"""

import requests
import json
import time
from datetime import datetime
from config import API_CONFIG, USER_CONFIG, LEVEL_DESCRIPTIONS, REQUEST_CONFIG, DEBUG_CONFIG

def printf(msg):
    """日志输出"""
    if DEBUG_CONFIG["enable_logging"]:
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        print(f"[{timestamp}] {msg}")

def make_request(method, url, **kwargs):
    """统一的请求处理"""
    headers = {
        'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 18_3_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3.1 Mobile/15E148 Safari/604.1',
        'Accept': '*/*',
        'Content-Type': 'application/x-www-form-urlencoded',
        'Referer': f'{API_CONFIG["base_url"]}/',
        'token': API_CONFIG["token"],
        'Accept-Language': 'zh-CN,zh-Hans;q=0.9',
        'Priority': 'u=3, i',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive'
    }
    
    kwargs['headers'] = headers
    kwargs['timeout'] = REQUEST_CONFIG["timeout"]
    
    if DEBUG_CONFIG["log_requests"]:
        printf(f"🔍 {method.upper()} {url}")
        if 'data' in kwargs:
            printf(f"📤 请求数据: {kwargs['data']}")
    
    try:
        response = requests.request(method, url, **kwargs)
        
        if DEBUG_CONFIG["log_responses"]:
            printf(f"📥 响应状态: {response.status_code}")
            if response.status_code == 200:
                try:
                    data = response.json()
                    printf(f"📥 响应数据: {json.dumps(data, ensure_ascii=False)}")
                except:
                    printf(f"📥 响应内容: {response.text[:200]}...")
        
        return response
        
    except Exception as e:
        printf(f"❌ 请求异常: {e}")
        return None

def get_current_user_info():
    """获取当前用户信息"""
    url = f"{API_CONFIG['base_url']}/api/user/getTeamInfo"
    response = make_request('GET', url)
    
    if response and response.status_code == 200:
        try:
            data = response.json()
            if data.get('code') == 0:
                return data.get('data', {})
        except:
            pass
    
    return None

def attempt_level_update(user_id, new_level):
    """尝试更新用户等级"""
    # 可能的API端点
    endpoints = [
        "/api/user/updateLevel",
        "/api/user/updateUserLevel", 
        "/api/admin/updateUserLevel",
        "/api/user/setLevel",
        "/api/user/upgradeLevel",
        "/api/user/modifyLevel"
    ]
    
    for endpoint in endpoints:
        url = f"{API_CONFIG['base_url']}{endpoint}"
        
        # 尝试不同的参数格式
        data_formats = [
            {'userId': user_id, 'levelId': new_level},
            {'user_id': user_id, 'level_id': new_level},
            {'id': user_id, 'level': new_level},
            {'userId': str(user_id), 'levelId': str(new_level)}
        ]
        
        for data in data_formats:
            printf(f"🔄 尝试: {endpoint} with {data}")
            response = make_request('POST', url, data=data)
            
            if response and response.status_code == 200:
                try:
                    result = response.json()
                    if result.get('code') == 0:
                        printf(f"✅ 更新成功！端点: {endpoint}")
                        return result
                    elif result.get('code') == -2:
                        printf(f"🔑 Token错误: {result.get('msg')}")
                        return None
                    else:
                        printf(f"❌ API错误: {result.get('msg', '未知错误')}")
                except:
                    printf(f"❌ 响应解析失败")
            
            time.sleep(1)  # 避免请求过快
    
    return None

def main():
    """主执行函数"""
    print("=" * 50)
    print("🚀 聚算云用户等级修改工具")
    print("=" * 50)
    
    # 显示配置信息
    printf(f"📊 配置信息:")
    printf(f"   API地址: {API_CONFIG['base_url']}")
    printf(f"   用户ID: {USER_CONFIG['user_id']}")
    printf(f"   当前等级: {USER_CONFIG['current_level']}")
    printf(f"   目标等级: {USER_CONFIG['target_level']}")
    
    # 显示等级信息
    current_desc = LEVEL_DESCRIPTIONS.get(USER_CONFIG['current_level'], {})
    target_desc = LEVEL_DESCRIPTIONS.get(USER_CONFIG['target_level'], {})
    
    printf(f"📈 等级变更:")
    printf(f"   从: {current_desc.get('name', '未知')} (佣金{current_desc.get('commission', '0%')})")
    printf(f"   到: {target_desc.get('name', '未知')} (佣金{target_desc.get('commission', '0%')})")
    
    if DEBUG_CONFIG["simulate_mode"]:
        printf("🎭 模拟模式已启用")
        time.sleep(2)
        printf("✅ 模拟更新成功！")
        return
    
    # 1. 获取当前用户信息
    printf("🔍 步骤1: 获取当前用户信息")
    user_info = get_current_user_info()
    
    if user_info:
        actual_user_id = user_info.get('userId')
        actual_level = user_info.get('levelId')
        
        printf(f"✅ 获取成功:")
        printf(f"   实际用户ID: {actual_user_id}")
        printf(f"   实际当前等级: {actual_level}")
        printf(f"   手机号: {user_info.get('phone')}")
        printf(f"   团队收益: {user_info.get('teamIncome')}")
        
        # 使用实际获取的信息
        user_id = actual_user_id
        current_level = actual_level
    else:
        printf("❌ 无法获取用户信息，使用配置文件中的值")
        user_id = USER_CONFIG['user_id']
        current_level = USER_CONFIG['current_level']
    
    # 2. 尝试更新等级
    printf(f"🔄 步骤2: 尝试更新用户等级")
    target_level = USER_CONFIG['target_level']
    
    if current_level == target_level:
        printf(f"⚠️  用户已经是目标等级 {target_level}，无需更新")
        return
    
    result = attempt_level_update(user_id, target_level)
    
    if result:
        printf("🎉 等级更新成功！")
        printf(f"📊 更新结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
        
        # 3. 验证更新
        printf("🔍 步骤3: 验证更新结果")
        time.sleep(3)
        
        updated_info = get_current_user_info()
        if updated_info:
            new_level = updated_info.get('levelId')
            if new_level == target_level:
                printf(f"✅ 验证成功！等级已更新为: {new_level}")
            else:
                printf(f"⚠️  等级未变更，当前仍为: {new_level}")
        else:
            printf("❌ 无法验证更新结果")
    else:
        printf("❌ 等级更新失败")
        printf("💡 可能的原因:")
        printf("   1. Token已过期或无效")
        printf("   2. 用户权限不足")
        printf("   3. API端点已变更")
        printf("   4. 业绩未达到升级要求")
    
    print("=" * 50)
    print("🏁 执行完成")
    print("=" * 50)

if __name__ == "__main__":
    main()