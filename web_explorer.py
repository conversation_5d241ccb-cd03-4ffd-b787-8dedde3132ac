#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
聚算云网站结构探索脚本
用于发现可能的管理功能和API端点
"""

import requests
import json
import time
from datetime import datetime
from bs4 import BeautifulSoup
import re

class WebExplorer:
    def __init__(self, base_url="https://cdn.jusuanyun.vip"):
        self.base_url = base_url
        self.session = requests.Session()
        self.token = ""
        
        # 设置通用请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 18_3_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3.1 Mobile/15E148 Safari/604.1',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh-Hans;q=0.9',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Referer': f'{base_url}/'
        })
    
    def printf(self, msg):
        """日志输出"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        print(f"[{timestamp}] {msg}")
    
    def login(self, username, password):
        """用户登录"""
        try:
            url = f"{self.base_url}/api/user/login"
            data = {'phone': username, 'password': password}
            
            response = self.session.post(url, data=data, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('code') == 0:
                    self.token = result.get('data', {}).get('token')
                    self.session.headers['token'] = self.token
                    self.printf(f"✅ 登录成功")
                    return True
            
            return False
            
        except Exception as e:
            self.printf(f"❌ 登录异常: {e}")
            return False
    
    def explore_main_page(self):
        """探索主页面结构"""
        try:
            self.printf("🔍 探索主页面...")
            
            response = self.session.get(self.base_url, timeout=30)
            
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # 查找JavaScript文件
                scripts = soup.find_all('script', src=True)
                self.printf(f"📄 发现 {len(scripts)} 个JavaScript文件:")
                
                js_files = []
                for script in scripts:
                    src = script.get('src')
                    if src:
                        if not src.startswith('http'):
                            src = f"{self.base_url}/{src.lstrip('/')}"
                        js_files.append(src)
                        self.printf(f"   📄 {src}")
                
                return js_files
            else:
                self.printf(f"❌ 无法访问主页: {response.status_code}")
                return []
                
        except Exception as e:
            self.printf(f"❌ 探索主页异常: {e}")
            return []
    
    def analyze_js_files(self, js_files):
        """分析JavaScript文件中的API端点"""
        try:
            self.printf("🔍 分析JavaScript文件...")
            
            api_patterns = [
                r'/api/[a-zA-Z0-9_/]+',
                r'"/[a-zA-Z0-9_/]+/[a-zA-Z0-9_/]+"',
                r"'/[a-zA-Z0-9_/]+/[a-zA-Z0-9_/]+'"
            ]
            
            found_apis = set()
            
            for js_file in js_files[:5]:  # 只分析前5个文件
                try:
                    self.printf(f"📄 分析: {js_file}")
                    response = self.session.get(js_file, timeout=30)
                    
                    if response.status_code == 200:
                        content = response.text
                        
                        # 搜索API模式
                        for pattern in api_patterns:
                            matches = re.findall(pattern, content)
                            for match in matches:
                                # 清理匹配结果
                                api = match.strip('"\'')
                                if '/api/' in api and len(api) > 5:
                                    found_apis.add(api)
                
                except Exception as e:
                    self.printf(f"❌ 分析文件失败: {e}")
                    continue
                
                time.sleep(1)
            
            if found_apis:
                self.printf(f"🎯 发现 {len(found_apis)} 个API端点:")
                for api in sorted(found_apis):
                    self.printf(f"   🔗 {api}")
                
                # 筛选可能的用户管理API
                user_apis = [api for api in found_apis if any(keyword in api.lower() for keyword in ['user', 'member', 'level', 'admin', 'manage'])]
                
                if user_apis:
                    self.printf(f"🎯 可能的用户管理API:")
                    for api in user_apis:
                        self.printf(f"   🔧 {api}")
                
                return list(found_apis)
            else:
                self.printf("❌ 未发现API端点")
                return []
                
        except Exception as e:
            self.printf(f"❌ 分析JavaScript文件异常: {e}")
            return []
    
    def test_discovered_apis(self, apis):
        """测试发现的API端点"""
        try:
            self.printf("🧪 测试发现的API端点...")
            
            # 筛选可能的等级相关API
            level_apis = [api for api in apis if any(keyword in api.lower() for keyword in ['level', 'upgrade', 'update', 'set', 'modify'])]
            
            if not level_apis:
                self.printf("❌ 未发现等级相关API")
                return []
            
            working_apis = []
            
            for api in level_apis:
                try:
                    url = f"{self.base_url}{api}" if not api.startswith('http') else api
                    
                    # 尝试不同的HTTP方法
                    methods = ['GET', 'POST', 'PUT', 'PATCH']
                    
                    for method in methods:
                        try:
                            if method == 'GET':
                                response = self.session.get(url, timeout=10)
                            else:
                                response = self.session.request(method, url, data={}, timeout=10)
                            
                            if response.status_code not in [404, 405]:
                                self.printf(f"✅ 可用端点: {method} {api} (状态码: {response.status_code})")
                                working_apis.append({'method': method, 'url': api, 'status': response.status_code})
                                
                                # 如果返回200，尝试解析响应
                                if response.status_code == 200:
                                    try:
                                        data = response.json()
                                        self.printf(f"   📥 响应: {json.dumps(data, ensure_ascii=False)[:100]}...")
                                    except:
                                        self.printf(f"   📥 响应: {response.text[:100]}...")
                                
                                break  # 找到可用方法就跳出
                            
                        except:
                            continue
                
                except Exception as e:
                    self.printf(f"❌ 测试API失败: {e}")
                    continue
                
                time.sleep(0.5)
            
            return working_apis
            
        except Exception as e:
            self.printf(f"❌ 测试API异常: {e}")
            return []
    
    def try_level_update_with_discovered_apis(self, working_apis, user_id=103, target_level=3):
        """使用发现的API尝试更新等级"""
        try:
            self.printf(f"🔄 使用发现的API尝试更新等级...")
            
            for api_info in working_apis:
                method = api_info['method']
                url = api_info['url']
                
                if method in ['POST', 'PUT', 'PATCH']:
                    self.printf(f"🔄 尝试: {method} {url}")
                    
                    # 尝试不同的参数组合
                    data_combinations = [
                        {'userId': user_id, 'levelId': target_level},
                        {'user_id': user_id, 'level_id': target_level},
                        {'id': user_id, 'level': target_level},
                        {'userId': str(user_id), 'levelId': str(target_level)}
                    ]
                    
                    for data in data_combinations:
                        try:
                            full_url = f"{self.base_url}{url}" if not url.startswith('http') else url
                            response = self.session.request(method, full_url, data=data, timeout=30)
                            
                            if response.status_code == 200:
                                try:
                                    result = response.json()
                                    if result.get('code') == 0:
                                        self.printf(f"🎉 等级更新成功！")
                                        self.printf(f"📊 使用API: {method} {url}")
                                        self.printf(f"📊 参数: {data}")
                                        self.printf(f"📊 响应: {result}")
                                        return True
                                    else:
                                        self.printf(f"❌ API错误: {result.get('msg', '未知错误')}")
                                except:
                                    self.printf(f"❌ 响应解析失败")
                            
                        except Exception as e:
                            continue
                        
                        time.sleep(1)
            
            return False
            
        except Exception as e:
            self.printf(f"❌ 尝试更新等级异常: {e}")
            return False

def main():
    """主函数"""
    print("=" * 60)
    print("🕵️ 聚算云网站结构探索工具")
    print("=" * 60)
    
    # 登录信息
    username = "13751504455"
    password = "Leo030340"
    
    # 创建探索器实例
    explorer = WebExplorer()
    
    # 1. 登录
    explorer.printf("🔐 步骤1: 用户登录")
    if not explorer.login(username, password):
        explorer.printf("❌ 登录失败，无法继续")
        return
    
    # 2. 探索主页面
    explorer.printf("🔍 步骤2: 探索网站结构")
    js_files = explorer.explore_main_page()
    
    if not js_files:
        explorer.printf("❌ 无法获取网站结构")
        return
    
    # 3. 分析JavaScript文件
    explorer.printf("🔍 步骤3: 分析API端点")
    discovered_apis = explorer.analyze_js_files(js_files)
    
    if not discovered_apis:
        explorer.printf("❌ 未发现API端点")
        return
    
    # 4. 测试发现的API
    explorer.printf("🧪 步骤4: 测试API端点")
    working_apis = explorer.test_discovered_apis(discovered_apis)
    
    if not working_apis:
        explorer.printf("❌ 未发现可用的API端点")
        return
    
    # 5. 尝试使用发现的API更新等级
    explorer.printf("🔄 步骤5: 尝试更新用户等级")
    success = explorer.try_level_update_with_discovered_apis(working_apis)
    
    if not success:
        explorer.printf("❌ 所有尝试都失败了")
        explorer.printf("💡 建议:")
        explorer.printf("   1. 检查是否需要管理员权限")
        explorer.printf("   2. 系统可能只允许自动升级")
        explorer.printf("   3. 可能需要通过前端界面操作")
    
    print("=" * 60)
    print("🏁 探索完成")
    print("=" * 60)

if __name__ == "__main__":
    main()