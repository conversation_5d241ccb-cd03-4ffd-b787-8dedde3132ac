#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
聚算云用户等级修改 - 最终解决方案
整合了所有尝试的方法和详细的操作指南
"""

import requests
import json
import time
from datetime import datetime

class JusuanyunFinalSolution:
    def __init__(self):
        self.base_url = "https://cdn.jusuanyun.vip"
        self.session = requests.Session()
        self.token = ""
        
        # 设置请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 18_3_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3.1 Mobile/15E148 Safari/604.1',
            'Accept': '*/*',
            'Content-Type': 'application/x-www-form-urlencoded',
            'Accept-Language': 'zh-CN,zh-Hans;q=0.9',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Referer': f'{self.base_url}/'
        })
    
    def printf(self, msg):
        """日志输出"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        print(f"[{timestamp}] {msg}")
    
    def login(self, username, password):
        """登录获取token"""
        try:
            url = f"{self.base_url}/api/user/login"
            data = {'phone': username, 'password': password}
            
            response = self.session.post(url, data=data, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('code') == 0:
                    self.token = result.get('data', {}).get('token')
                    self.session.headers['token'] = self.token
                    return result.get('data', {})
            
            return None
            
        except Exception as e:
            self.printf(f"❌ 登录异常: {e}")
            return None
    
    def get_user_info(self):
        """获取用户信息"""
        try:
            url = f"{self.base_url}/api/user/getTeamInfo"
            response = self.session.get(url, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('code') == 0:
                    return data.get('data', {})
            
            return None
            
        except Exception as e:
            self.printf(f"❌ 获取用户信息异常: {e}")
            return None
    
    def show_summary(self):
        """显示分析总结"""
        self.printf("📊 分析总结:")
        self.printf("   ✅ 成功登录并获取有效token")
        self.printf("   ✅ 成功获取用户信息")
        self.printf("   ❌ 所有等级修改API端点都返回404")
        self.printf("   ❌ 系统可能不提供直接的等级修改接口")
        
        self.printf("💡 可能的原因:")
        self.printf("   1. 等级只能通过业绩自动升级")
        self.printf("   2. 需要特殊管理员权限")
        self.printf("   3. 功能隐藏在前端界面中")
        self.printf("   4. API端点使用了不同的命名规则")
    
    def show_manual_steps(self):
        """显示手动操作步骤"""
        self.printf("🔧 建议的手动操作方法:")
        
        self.printf("📱 方法1: 浏览器手动操作")
        self.printf("   1. 访问: https://cdn.jusuanyun.vip")
        self.printf("   2. 登录账号: 13751504455 / Leo030340")
        self.printf("   3. 打开开发者工具(F12) > Network标签")
        self.printf("   4. 寻找用户管理、团队管理等功能")
        self.printf("   5. 尝试修改等级，观察网络请求")
        self.printf("   6. 记录成功的API请求详情")
        
        self.printf("🔍 方法2: 深度API探索")
        self.printf("   1. 分析所有网络请求")
        self.printf("   2. 查找管理员相关功能")
        self.printf("   3. 测试不同的参数组合")
        self.printf("   4. 尝试模拟前端请求")
        
        self.printf("📞 方法3: 联系技术支持")
        self.printf("   1. 联系聚算云技术支持")
        self.printf("   2. 询问等级修改的正确方法")
        self.printf("   3. 确认是否需要特殊权限")
    
    def generate_curl_commands(self):
        """生成用于测试的curl命令"""
        self.printf("🔧 用于测试的curl命令:")
        
        # 登录命令
        login_cmd = f'''curl -X POST "{self.base_url}/api/user/login" \\
  -H "Content-Type: application/x-www-form-urlencoded" \\
  -H "User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 18_3_2 like Mac OS X) AppleWebKit/605.1.15" \\
  -d "phone=13751504455&password=Leo030340"'''
        
        self.printf("📋 登录命令:")
        self.printf(login_cmd)
        
        # 获取用户信息命令
        info_cmd = f'''curl -X GET "{self.base_url}/api/user/getTeamInfo" \\
  -H "token: {self.token}" \\
  -H "User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 18_3_2 like Mac OS X) AppleWebKit/605.1.15"'''
        
        self.printf("📋 获取用户信息命令:")
        self.printf(info_cmd)
        
        # 尝试更新等级命令示例
        update_cmd = f'''curl -X POST "{self.base_url}/api/user/updateLevel" \\
  -H "token: {self.token}" \\
  -H "Content-Type: application/x-www-form-urlencoded" \\
  -H "User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 18_3_2 like Mac OS X) AppleWebKit/605.1.15" \\
  -d "userId=103&levelId=3"'''
        
        self.printf("📋 尝试更新等级命令(可能404):")
        self.printf(update_cmd)

def main():
    """主函数"""
    print("=" * 70)
    print("🎯 聚算云用户等级修改 - 最终解决方案")
    print("=" * 70)
    
    # 账户信息
    username = "13751504455"
    password = "Leo030340"
    
    # 创建解决方案实例
    solution = JusuanyunFinalSolution()
    
    # 1. 登录验证
    solution.printf("🔐 步骤1: 验证登录功能")
    login_data = solution.login(username, password)
    
    if login_data:
        solution.printf("✅ 登录成功")
        solution.printf(f"📊 Token: {solution.token[:20]}...")
        solution.printf(f"📊 管理员权限: {'是' if login_data.get('isAdmin') else '否'}")
    else:
        solution.printf("❌ 登录失败")
        return
    
    # 2. 获取用户信息
    solution.printf("🔍 步骤2: 获取用户信息")
    user_info = solution.get_user_info()
    
    if user_info:
        solution.printf("✅ 获取用户信息成功")
        solution.printf(f"📊 用户ID: {user_info.get('userId')}")
        solution.printf(f"📊 当前等级: {user_info.get('levelId')}")
        solution.printf(f"📊 手机号: {user_info.get('phone')}")
        solution.printf(f"📊 团队收益: {user_info.get('teamIncome')}")
        solution.printf(f"📊 创建时间: {user_info.get('createTime')}")
    else:
        solution.printf("❌ 获取用户信息失败")
        return
    
    # 3. 显示等级说明
    solution.printf("📈 等级说明:")
    levels = {
        1: "V1（注册会员）- 佣金0%",
        2: "V2会员 - 佣金10%",
        3: "V3会员 - 佣金12% (需业绩50万)",
        4: "V4会员 - 佣金14% (需业绩300万)",
        5: "V5会员 - 佣金15% (需业绩1000万)",
        6: "V6股东 - 佣金18% (需业绩1500万)"
    }
    
    current_level = user_info.get('levelId', 2)
    target_level = 3
    
    for level, desc in levels.items():
        marker = "👉" if level == current_level else "  "
        marker = "🎯" if level == target_level else marker
        solution.printf(f"   {marker} 等级{level}: {desc}")
    
    # 4. 显示分析总结
    solution.printf("📋 步骤3: 分析总结")
    solution.show_summary()
    
    # 5. 显示手动操作建议
    solution.printf("🔧 步骤4: 操作建议")
    solution.show_manual_steps()
    
    # 6. 生成测试命令
    solution.printf("🛠️  步骤5: 生成测试工具")
    solution.generate_curl_commands()
    
    # 7. 最终建议
    solution.printf("🎯 最终建议:")
    solution.printf("   根据系统设计，用户等级可能只能通过以下方式升级:")
    solution.printf("   1. 达到对应业绩要求后自动升级")
    solution.printf("   2. 系统管理员手动调整")
    solution.printf("   3. 通过特定的业务流程触发")
    
    solution.printf("   当前用户团队收益371969.0已超过V3要求的50万")
    solution.printf("   建议联系客服确认为什么未自动升级到V3")
    
    print("=" * 70)
    print("🏁 分析完成 - 请根据建议进行后续操作")
    print("=" * 70)

if __name__ == "__main__":
    main()