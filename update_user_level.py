#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
用户等级修改脚本
用于修改聚算云用户等级
"""

import requests
import json
import time
from datetime import datetime

class UserLevelUpdater:
    def __init__(self, base_url="http://cdn.jusuanyun.vip", token=""):
        self.base_url = base_url
        self.token = token
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 18_3_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3.1 Mobile/15E148 Safari/604.1',
            'Accept': '*/*',
            'Content-Type': 'application/x-www-form-urlencoded',
            'Referer': f'{base_url}/',
            'token': token,
            'Accept-Language': 'zh-CN,zh-Hans;q=0.9',
            'Priority': 'u=3, i',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive'
        }
    
    def printf(self, msg):
        """日志输出"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        print(f"[{timestamp}] {msg}")
    
    def get_team_info(self):
        """获取团队信息"""
        try:
            url = f"{self.base_url}/api/user/getTeamInfo"
            self.printf(f"🔍 获取团队信息: {url}")
            
            response = requests.get(url, headers=self.headers, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                self.printf(f"✅ 获取团队信息成功")
                return data
            else:
                self.printf(f"❌ 获取团队信息失败，状态码: {response.status_code}")
                return None
                
        except Exception as e:
            self.printf(f"❌ 获取团队信息异常: {e}")
            return None
    
    def get_team_list(self, page_number=1, page_size=5):
        """获取团队列表"""
        try:
            url = f"{self.base_url}/api/user/getTeamList"
            params = {
                'pageNumber': page_number,
                'pageSize': page_size
            }
            
            self.printf(f"🔍 获取团队列表: {url}")
            
            response = requests.get(url, headers=self.headers, params=params, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                self.printf(f"✅ 获取团队列表成功")
                return data
            else:
                self.printf(f"❌ 获取团队列表失败，状态码: {response.status_code}")
                return None
                
        except Exception as e:
            self.printf(f"❌ 获取团队列表异常: {e}")
            return None
    
    def update_user_level(self, user_id, new_level_id):
        """更新用户等级"""
        try:
            # 尝试不同的可能API端点
            possible_endpoints = [
                "/api/user/updateLevel",
                "/api/user/updateUserLevel", 
                "/api/admin/updateUserLevel",
                "/api/user/setLevel",
                "/api/user/upgradeLevel"
            ]
            
            for endpoint in possible_endpoints:
                url = f"{self.base_url}{endpoint}"
                self.printf(f"🔄 尝试更新用户等级: {url}")
                
                # 尝试POST请求
                data = {
                    'userId': user_id,
                    'levelId': new_level_id
                }
                
                try:
                    response = requests.post(url, headers=self.headers, data=data, timeout=30)
                    
                    if response.status_code == 200:
                        result = response.json()
                        if result.get('code') == 0:
                            self.printf(f"✅ 用户等级更新成功: {endpoint}")
                            return result
                        else:
                            self.printf(f"❌ API返回错误: {result.get('msg', '未知错误')}")
                    else:
                        self.printf(f"❌ HTTP错误，状态码: {response.status_code}")
                        
                except requests.exceptions.RequestException as e:
                    self.printf(f"❌ 请求异常: {e}")
                    continue
            
            self.printf(f"❌ 所有API端点都尝试失败")
            return None
            
        except Exception as e:
            self.printf(f"❌ 更新用户等级异常: {e}")
            return None
    
    def simulate_level_update(self, user_id, current_level, target_level):
        """模拟等级更新（用于测试）"""
        try:
            self.printf(f"🎭 模拟等级更新")
            self.printf(f"   用户ID: {user_id}")
            self.printf(f"   当前等级: {current_level}")
            self.printf(f"   目标等级: {target_level}")
            
            # 等级描述
            level_desc = {
                1: "V1（注册会员）",
                2: "V2会员 - 推荐佣金10%，渠道收益10%",
                3: "V3会员 - 推荐佣金12%，渠道收益12%（需累计业绩50万）",
                4: "V4会员 - 推荐佣金14%，渠道收益14%（需累计业绩300万）",
                5: "V5会员 - 推荐佣金15%，渠道收益15%（需累计业绩1000万）",
                6: "V6股东 - 推荐佣金18%，渠道收益18%（需累计业绩1500万）"
            }
            
            self.printf(f"📊 等级变更详情:")
            self.printf(f"   从: {level_desc.get(current_level, f'未知等级{current_level}')}")
            self.printf(f"   到: {level_desc.get(target_level, f'未知等级{target_level}')}")
            
            # 模拟成功响应
            mock_response = {
                "code": 0,
                "msg": "SUCCESS",
                "data": {
                    "userId": user_id,
                    "oldLevelId": current_level,
                    "newLevelId": target_level,
                    "updateTime": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                }
            }
            
            time.sleep(2)  # 模拟网络延迟
            self.printf(f"✅ 模拟更新成功")
            return mock_response
            
        except Exception as e:
            self.printf(f"❌ 模拟更新异常: {e}")
            return None

def main():
    """主函数"""
    print("=" * 60)
    print("🚀 聚算云用户等级修改脚本")
    print("=" * 60)
    
    # 配置信息
    token = "7e2820ff7d6273fbae54c23a21ea0be35613fc6bdc7ee6473672d6dace8f98fa"
    user_id = 103  # 从API响应中获取的用户ID
    current_level = 2  # 当前等级
    target_level = 3   # 目标等级
    
    # 创建更新器实例
    updater = UserLevelUpdater(token=token)
    
    # 1. 首先获取当前团队信息
    updater.printf("🔍 步骤1: 获取当前团队信息")
    team_info = updater.get_team_info()
    
    if team_info and team_info.get('code') == 0:
        data = team_info.get('data', {})
        current_user_id = data.get('userId')
        current_level_id = data.get('levelId')
        
        updater.printf(f"📊 当前用户信息:")
        updater.printf(f"   用户ID: {current_user_id}")
        updater.printf(f"   当前等级: {current_level_id}")
        updater.printf(f"   手机号: {data.get('phone')}")
        updater.printf(f"   团队收益: {data.get('teamIncome')}")
        updater.printf(f"   团队人数: {data.get('teamNum')}")
        
        # 更新实际的用户ID和等级
        user_id = current_user_id
        current_level = current_level_id
        
    else:
        updater.printf("❌ 无法获取团队信息，使用默认配置")
    
    # 2. 尝试更新用户等级
    updater.printf(f"🔄 步骤2: 尝试更新用户等级 {current_level} -> {target_level}")
    
    # 首先尝试真实API调用
    result = updater.update_user_level(user_id, target_level)
    
    if result:
        updater.printf("✅ 用户等级更新成功！")
        updater.printf(f"📊 更新结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
    else:
        updater.printf("❌ 真实API调用失败，使用模拟模式")
        # 如果真实API失败，使用模拟模式
        result = updater.simulate_level_update(user_id, current_level, target_level)
        
        if result:
            updater.printf("🎭 模拟更新完成！")
            updater.printf(f"📊 模拟结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
    
    # 3. 验证更新结果
    updater.printf("🔍 步骤3: 验证更新结果")
    time.sleep(2)
    
    updated_team_info = updater.get_team_info()
    if updated_team_info and updated_team_info.get('code') == 0:
        new_data = updated_team_info.get('data', {})
        new_level_id = new_data.get('levelId')
        
        if new_level_id == target_level:
            updater.printf(f"✅ 验证成功！用户等级已更新为: {new_level_id}")
        else:
            updater.printf(f"⚠️  等级未变更，当前仍为: {new_level_id}")
    else:
        updater.printf("❌ 无法验证更新结果")
    
    print("=" * 60)
    print("🏁 脚本执行完成")
    print("=" * 60)

if __name__ == "__main__":
    main()