# [create_at: 2025-01-08 00:00:00]
# [open_source: true]
# [public: true]
# [title: 求知]
# [version: 1.0.0]
# [author: AI Assistant]
# [service: 未解之谜、世界之最、奇闻异事内容获取]
# [rule:raw 求知]
# [rule:raw 1]
# [rule:raw 2]
# [rule:raw 3]
# [admin: false]
# [disable: false]
# [price: 0.0]
# [description: 获取未解之谜、世界之最、奇闻异事等求知内容]
# [icon: 🔍]
# [param: {"required":true,"key":"qiuzhi.api_url","bool":false,"placeholder":"http://api.yujn.cn/api/wjzm.php","name":"请求地址","desc":"API请求地址"}]
# [param: {"required":false,"key":"qiuzhi.push_target","bool":false,"placeholder":"qqgroup:123,wxgroup:123,tggroup:-100123","name":"推送目标","desc":"推送目标格式，多个用逗号分隔"}]
# [param: {"required":false,"key":"qiuzhi.schedule","bool":false,"placeholder":"0 9 * * *","name":"执行定时","desc":"定时执行的cron表达式"}]

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import time
import html
from datetime import datetime

# autMan 全局对象声明
try:
    import middleware
    sender = middleware.Sender(middleware.getSenderID())
except ImportError:
    class MockSender:
        def getMessage(self): return "求知"
        def reply(self, msg): print(f"回复: {msg}")
    sender = MockSender()

# 使用middleware bucket管理用户状态

def printf(msg):
    """日志输出"""
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    print(f"[{timestamp}] {msg}")

def get_config():
    """获取插件配置"""
    try:
        api_url = middleware.bucketGet("qiuzhi", "api_url") or "http://api.yujn.cn/api/wjzm.php"
        push_target = middleware.bucketGet("qiuzhi", "push_target") or ""
        schedule = middleware.bucketGet("qiuzhi", "schedule") or ""
        return api_url, push_target, schedule
    except:
        return "http://api.yujn.cn/api/wjzm.php", "", ""

def fetch_content(content_type):
    """获取内容"""
    try:
        api_url, _, _ = get_config()
        
        # 根据类型设置参数
        type_map = {
            "1": "wjzm",  # 未解之谜
            "2": "sjzz",  # 世界之最
            "3": "qwys"   # 奇闻异事
        }
        
        if content_type not in type_map:
            return None
        
        # 发送GET请求，增加重试机制
        params = {"type": type_map[content_type]}
        printf(f"🔍 请求内容类型: {content_type} -> {type_map[content_type]}")
        
        max_retries = 3
        for attempt in range(max_retries):
            try:
                printf(f"🔄 第{attempt + 1}次尝试请求...")
                response = requests.get(api_url, params=params, timeout=30)
                
                if response.status_code == 200:
                    data = response.json()
                    printf(f"✅ 请求成功，返回数据: {len(str(data))}字符")
                    return data
                else:
                    printf(f"❌ 请求失败，状态码: {response.status_code}")
                    if attempt < max_retries - 1:
                        printf(f"🔄 等待3秒后重试...")
                        time.sleep(3)
                        continue
                    
            except requests.exceptions.Timeout:
                printf(f"⏰ 第{attempt + 1}次请求超时")
                if attempt < max_retries - 1:
                    printf(f"🔄 等待3秒后重试...")
                    time.sleep(3)
                    continue
                    
            except requests.exceptions.ConnectionError:
                printf(f"🌐 第{attempt + 1}次连接错误")
                if attempt < max_retries - 1:
                    printf(f"🔄 等待5秒后重试...")
                    time.sleep(5)
                    continue
                    
            except Exception as e:
                printf(f"❌ 第{attempt + 1}次请求异常: {e}")
                if attempt < max_retries - 1:
                    printf(f"🔄 等待3秒后重试...")
                    time.sleep(3)
                    continue
        
        printf(f"❌ 经过{max_retries}次重试仍然失败，放弃请求")
        return None
            
    except Exception as e:
        printf(f"❌ 获取内容异常: {e}")
        return None

def format_content(data):
    """格式化返回内容"""
    try:
        if not data:
            return "❌ 未获取到内容"
        
        # 提取需要的字段
        time_str = data.get("time", "")
        title = data.get("title", "")
        content = data.get("content", "")
        
        # 处理内容中的换行符和HTML实体编码
        if content:
            # 替换换行符为制表符
            content = content.replace("\\n", "\t").replace("\n", "\t")
            
            # 使用html.unescape处理HTML实体编码
            content = html.unescape(content)
            
            # 格式化内容，添加段落分隔，美化阅读
            # 将制表符替换为换行，每60个字符左右换行
            content_lines = []
            sentences = content.split('\t')
            
            for sentence in sentences:
                if len(sentence.strip()) > 0:
                    # 每60个字符左右换行
                    while len(sentence) > 60:
                        # 找到合适的断句点
                        break_point = 60
                        for i in range(50, min(70, len(sentence))):
                            if sentence[i] in '，。！？；：':
                                break_point = i + 1
                                break
                        content_lines.append(sentence[:break_point])
                        sentence = sentence[break_point:]
                    if sentence.strip():
                        content_lines.append(sentence)
            
            content = '\n'.join(content_lines)
        
        # 构建结果部分数组
        result_parts = []
        
        if time_str:
            result_parts.append(f"📅 时间: {time_str}")
        
        if title:
            result_parts.append(f"📖 标题: {title}")
        
        if content:
            result_parts.append(f"📝 内容:\n{content}")
        
        # 使用换行符分隔各部分
        formatted_text = "\n".join(result_parts)
        
        return formatted_text
        
    except Exception as e:
        printf(f"❌ 格式化异常: {e}")
        return f"❌ 数据格式化失败: {str(data)}"

def show_menu():
    """显示菜单"""
    menu = """🔍 求知菜单
请选择您想了解的内容：

1️⃣ 未解之谜
2️⃣ 世界之最  
3️⃣ 奇闻异事

请回复数字选择："""
    return menu

def get_user_state():
    """获取用户状态"""
    try:
        sender_id = middleware.getSenderID()
        state_key = f"qiuzhi_state_{sender_id}"
        state = middleware.bucketGet("qiuzhi_temp", state_key) or ""
        printf(f"🔍 获取用户状态: {sender_id} -> {state}")
        return state
    except Exception as e:
        printf(f"❌ 获取用户状态失败: {e}")
        return ""

def set_user_state(state):
    """设置用户状态"""
    try:
        sender_id = middleware.getSenderID()
        state_key = f"qiuzhi_state_{sender_id}"
        middleware.bucketSet("qiuzhi_temp", state_key, state)
        printf(f"🔧 设置用户状态: {sender_id} -> {state}")
    except Exception as e:
        printf(f"❌ 设置用户状态失败: {e}")

def clear_user_state():
    """清除用户状态"""
    try:
        sender_id = middleware.getSenderID()
        state_key = f"qiuzhi_state_{sender_id}"
        middleware.bucketSet("qiuzhi_temp", state_key, "")
    except:
        pass

def main():
    """主函数 - 使用状态管理确保正确的交互流程"""
    try:
        user_input = sender.getMessage().strip()
        printf(f"🚀 收到输入: {user_input}")
        
        # 如果是触发词"求知"，显示菜单并设置状态
        if user_input == "求知":
            sender.reply(show_menu())
            set_user_state("waiting_choice")
            printf(f"✅ 菜单显示成功，等待用户选择")
            return
        
        # 如果输入的是数字选择，检查用户状态
        if user_input in ["1", "2", "3"]:
            user_state = get_user_state()
            
            # 只有用户处于等待选择状态时才处理数字输入
            if user_state == "waiting_choice":
                printf(f"✅ 处理用户选择: {user_input}")
                
                type_names = {"1": "未解之谜", "2": "世界之最", "3": "奇闻异事"}
                sender.reply(f"🔍 正在获取{type_names[user_input]}内容，请稍候...")
                
                # 获取内容
                data = fetch_content(user_input)
                
                if data:
                    formatted_result = format_content(data)
                    sender.reply(formatted_result)
                    printf(f"✅ 内容发送成功")
                else:
                    sender.reply("❌ 获取内容失败，可能是网络问题或服务器繁忙，请稍后重试")
                
                # 清除用户状态
                clear_user_state()
                return
            else:
                # 用户没有先输入"求知"，忽略数字输入
                printf(f"🔄 用户未处于选择状态，忽略数字输入")
                return
        
        # 不匹配的输入，静默退出
        return
        
    except Exception as e:
        printf(f"❌ 插件异常: {e}")
        sender.reply("❌ 插件执行异常，请稍后重试")

if __name__ == "__main__":
    main()