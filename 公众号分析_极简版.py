#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# 公众号分析极简版 - 微信公众号文章AI分析插件

import requests
import re
import json
import time
from datetime import datetime

# autMan 全局对象声明
try:
    import middleware
    sender = middleware.Sender(middleware.getSenderID())
except ImportError:
    class MockSender:
        def getMessage(self): return "https://mp.weixin.qq.com/s/JoHwQPj78TDjrDdU6esPAg"
        def reply(self, msg): print(f"回复: {msg}")
    sender = MockSender()

def printf(msg):
    """日志输出"""
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    print(f"[{timestamp}] {msg}")

def extract_wechat_content(url):
    """提取微信文章内容 - 性能优化版"""
    try:
        printf(f"🔍 提取内容: {url[:50]}...")

        # 优化：只使用最有效的User-Agent，减少尝试次数
        headers = {
            'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Cache-Control': 'no-cache'
        }

        # 创建session以复用连接，提高性能
        session = requests.Session()
        session.headers.update(headers)

        response = session.get(url, timeout=10)  # 减少超时时间从20秒到10秒
        printf(f"📊 HTTP状态: {response.status_code}, 内容长度: {len(response.text)}")

        if response.status_code != 200:
            # 如果第一次失败，尝试备用User-Agent
            printf(f"🔄 尝试备用User-Agent...")
            session.headers.update({
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            })
            response = session.get(url, timeout=8)
            if response.status_code != 200:
                return None, None

        html = response.text

        # 快速检查内容有效性
        if len(html) < 1000 or '验证' in html[:500]:
            printf(f"⚠️ 内容无效或需要验证")
            return None, None

        # 优化的标题提取 - 使用编译的正则表达式提高性能
        title = ""
        title_patterns = [
            re.compile(r'var msg_title = "([^"]*)"', re.IGNORECASE),
            re.compile(r'"title":"([^"]*)"', re.IGNORECASE),
            re.compile(r'<h1[^>]*class=[^>]*rich_media_title[^>]*>(.*?)</h1>', re.IGNORECASE | re.DOTALL),
            re.compile(r'<h1[^>]*>(.*?)</h1>', re.IGNORECASE | re.DOTALL)
        ]

        for pattern in title_patterns:
            match = pattern.search(html)
            if match:
                title = match.group(1).strip()
                title = re.sub(r'<[^>]+>', '', title)
                title = re.sub(r'\s+', ' ', title).strip()
                if title and len(title) > 3:
                    printf(f"✅ 标题提取成功: {title[:30]}...")
                    break

        # 优化的内容提取 - 使用编译的正则表达式
        content = ""
        content_patterns = [
            re.compile(r'<div[^>]*id="js_content"[^>]*>(.*?)</div>', re.IGNORECASE | re.DOTALL),
            re.compile(r'<div[^>]*class="rich_media_content"[^>]*>(.*?)</div>', re.IGNORECASE | re.DOTALL)
        ]

        for pattern in content_patterns:
            match = pattern.search(html)
            if match:
                raw_content = match.group(1)
                content = re.sub(r'<[^>]+>', '', raw_content)
                content = re.sub(r'\s+', ' ', content).strip()
                if content and len(content) > 100:
                    printf(f"✅ 内容提取成功: {len(content)}字符")
                    break

        printf(f"📝 最终结果 - 标题: {len(title)}字符, 内容: {len(content)}字符")

        # 如果有标题或内容，就认为成功
        if title or (content and len(content) > 50):
            if not title:
                title = "微信公众号文章"
            return title, content[:2000]  # 进一步限制内容长度以提高AI处理速度

        return None, None

    except Exception as e:
        printf(f"❌ 内容提取异常: {e}")
        return None, None

def analyze_with_gemini(title, content):
    """使用Gemini分析文章"""
    try:
        printf(f"🤖 开始AI分析...")
        
        # 获取配置
        try:
            api_key = middleware.bucketGet("aigzh", "api_key") or "sk-72117476b8c04cc6a026b17f79a2339e"
            base_url = middleware.bucketGet("aigzh", "base_url") or "https://api.deepseek.com/v1"
            model = middleware.bucketGet("aigzh", "model") or "deepseek-chat"
        except:
            api_key = "sk-eqyHojXr9c5zuC9t38ISML0FRhvJAJilQOxSxL4tU3HDf7S8"
            base_url = "https://tbai.xin/v1"
            model = "gemini-2.5-pro"
        
        # 优化后的简洁prompt，减少token消耗，提高响应速度
        prompt = f"""分析文章并返回JSON：
标题：{title}
内容：{content[:1500]}

返回格式：
{{"overview":"概述(50字内)","key_points":["要点1","要点2","要点3"],"value_assessment":{{"score":评分1-10,"readability":"可读性评价"}},"actionable_insights":["建议1","建议2"],"tags":["标签1","标签2","标签3"]}}"""

        # 优化的重试机制，减少等待时间
        for attempt in range(2):  # 减少重试次数从3次到2次
            try:
                if attempt > 0:
                    delay = 1  # 减少重试延迟从指数增长到固定1秒
                    printf(f"⏳ 第{attempt + 1}次尝试，等待{delay}秒...")
                    time.sleep(delay)

                headers = {
                    'Authorization': f'Bearer {api_key}',
                    'Content-Type': 'application/json'
                }

                # 优化API参数以提高响应速度
                data = {
                    'model': model,
                    'messages': [{'role': 'user', 'content': prompt}],
                    'temperature': 0.3,  # 降低temperature提高响应速度和一致性
                    'max_tokens': 800,   # 减少max_tokens从2000到800
                    'top_p': 0.9,       # 添加top_p参数优化生成质量
                    'stream': False     # 确保非流式响应
                }

                response = requests.post(f"{base_url}/chat/completions", headers=headers, json=data, timeout=20)  # 减少超时时间
                
                if response.status_code == 200:
                    result = response.json()
                    if 'choices' in result and result['choices']:
                        content = result['choices'][0]['message']['content']
                        return parse_ai_response(content)
                elif response.status_code == 429:
                    printf(f"⚠️ API速率限制，重试中...")
                    continue
                else:
                    printf(f"❌ API调用失败: {response.status_code}")
                    
            except Exception as e:
                printf(f"⚠️ API调用异常: {e}")
                continue
        
        printf(f"❌ AI分析失败")
        return None
        
    except Exception as e:
        printf(f"❌ AI分析异常: {e}")
        return None

def parse_ai_response(content):
    """解析AI响应 - 性能优化版"""
    try:
        # 优化：使用编译的正则表达式，优先匹配最常见的格式
        json_patterns = [
            re.compile(r'\{[^{}]*"overview"[^{}]*\}', re.DOTALL),  # 直接匹配包含overview的JSON
            re.compile(r'```json\s*(.*?)\s*```', re.DOTALL),
            re.compile(r'\{.*?\}', re.DOTALL)
        ]

        json_str = None
        for pattern in json_patterns:
            match = pattern.search(content)
            if match:
                json_str = match.group(1) if 'json' in pattern.pattern else match.group(0)
                break

        if not json_str:
            return None

        # 快速JSON修复和解析
        try:
            return json.loads(json_str)
        except json.JSONDecodeError:
            # 快速修复常见JSON问题
            json_str = re.sub(r',(\s*[}\]])', r'\1', json_str)  # 移除尾随逗号
            json_str = re.sub(r'([{,]\s*)(\w+):', r'\1"\2":', json_str)  # 添加缺失的引号
            try:
                return json.loads(json_str)
            except:
                return None

    except Exception as e:
        printf(f"❌ 响应解析失败: {e}")
        return None

def format_result(analysis):
    """格式化分析结果 - 使用分隔符提供更好的阅读性"""
    try:
        parts = []

        # 概述
        if 'overview' in analysis:
            parts.append(f"📖 概述\n{analysis['overview']}")

        # 关键要点
        if 'key_points' in analysis and analysis['key_points']:
            key_points_section = ["🔑 关键要点"]
            for i, point in enumerate(analysis['key_points'], 1):
                key_points_section.append(f"{i}. {point}")
            parts.append('\n'.join(key_points_section))

        # 价值评估
        if 'value_assessment' in analysis:
            assessment = analysis['value_assessment']
            if isinstance(assessment, dict):
                score = assessment.get('score', 'N/A')
                value_section = [f"⭐ 价值评估: {score}/10"]

                readability = assessment.get('readability', '')
                if readability:
                    value_section.append(f"📖 可读性: {readability}")

                parts.append('\n'.join(value_section))

        # 可执行建议
        if 'actionable_insights' in analysis and analysis['actionable_insights']:
            insights_section = ["💡 建议"]
            for i, insight in enumerate(analysis['actionable_insights'], 1):
                insights_section.append(f"{i}. {insight}")
            parts.append('\n'.join(insights_section))

        # 标签
        if 'tags' in analysis and analysis['tags']:
            tags = ' '.join([f"#{tag}" for tag in analysis['tags']])
            parts.append(f"🏷️ 标签\n{tags}")

        # 使用 --- 分隔符连接各部分，提供清晰的视觉分隔
        return '\n----------------------------\n'.join(parts)

    except Exception as e:
        printf(f"❌ 格式化失败: {e}")
        return str(analysis)

def extract_url_from_xml(xml_content):
    """从XML消息中提取URL"""
    try:
        # 尝试从XML中提取URL
        url_match = re.search(r'<url>(.*?)</url>', xml_content, re.IGNORECASE | re.DOTALL)
        if url_match:
            url = url_match.group(1)
            # 解码HTML实体
            url = url.replace('&amp;', '&')
            url = url.replace('&lt;', '<')
            url = url.replace('&gt;', '>')
            url = url.replace('&quot;', '"')
            return url

        # 如果XML提取失败，尝试正则表达式
        url_match = re.search(r'https?://[^\s<>&]+', xml_content)
        if url_match:
            return url_match.group(0)

        return None
    except Exception as e:
        printf(f"❌ URL提取异常: {e}")
        return None

def main():
    """主函数 - 性能优化版"""
    try:
        user_input = sender.getMessage()
        printf(f"🚀 收到输入: {user_input[:100]}...")  # 减少日志输出

        # 快速检查是否包含微信链接
        if "mp.weixin.qq.com" not in user_input:
            return  # 静默退出，减少不必要的日志

        # 快速提取链接
        url = extract_url_from_xml(user_input)
        if not url:
            return  # 静默退出

        printf(f"✅ 检测到链接")

        # 并行处理：提取内容
        title, content = extract_wechat_content(url)
        if not content:
            sender.reply("❌ 无法提取文章内容，请检查链接是否有效")
            return

        printf(f"✅ 内容提取成功: {len(content)}字符")

        # AI分析
        analysis = analyze_with_gemini(title, content)
        if not analysis:
            sender.reply("❌ AI分析失败，请稍后重试")
            return

        printf(f"✅ AI分析成功")

        # 快速格式化并返回结果
        formatted_result = format_result(analysis)
        sender.reply(formatted_result)
        printf(f"✅ 分析完成")

    except Exception as e:
        printf(f"❌ 插件异常: {e}")
        sender.reply("❌ 插件执行异常，请稍后重试")

if __name__ == "__main__":
    main()
