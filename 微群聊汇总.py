# [create_at: 2025-01-21 20:30:00]
# [open_source: true]
# [public: true]
# [title: 微群聊汇总]
# [version: 1.1.0]
# [author: AI Assistant]
# [service: 微信群聊消息AI汇总分析]
# [rule:raw 汇总群聊]
# [rule:raw 群聊汇总]
# [rule:raw 分析群聊]
# [rule:raw 群聊分析]
# [rule:raw 获取群聊]
# [admin: false]
# [disable: false]
# [price: 0.0]
# [description: 智能分析微信群聊消息，支持从日志文件、URL或直接输入获取群聊内容进行汇总分析]
# [icon: 💬]
# [param: {"required":true,"key":"WXQUNLIAO.api_key","bool":false,"placeholder":"sk-xxx","name":"API密钥","desc":"对应模型API密钥"}]
# [param: {"required":true,"key":"WXQUNLIAO.base_url","bool":false,"placeholder":"https://tbai.xin/v1","name":"API基础URL","desc":"对应API的基础URL地址，Deepseek选择：https://api.deepseek.com/v1"}]
# [param: {"required":true,"key":"WXQUNLIAO.model","bool":false,"placeholder":"gemini-2.5-pro","name":"AI模型","desc":"Gemini模型选择：gemini-2.5-pro；Deepseek模型选择：deepseek-chat"}]
# [param: {"required":false,"key":"QUNLIAO.min_messages","bool":false,"placeholder":"10","name":"最少消息数","desc":"触发汇总分析的最少消息数量，默认10条"}]
# [param: {"required":false,"key":"QUNLIAO.max_messages","bool":false,"placeholder":"100","name":"最大消息数","desc":"单次分析的最大消息数量，默认100条"}]
# [param: {"required":false,"key":"QUNLIAO.log_url","bool":false,"placeholder":"http://192.168.50.43:12138","name":"日志服务器URL","desc":"微信框架日志服务器地址，用于自动获取群聊消息"}]

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import re
import json
import time
import os
from datetime import datetime, timedelta
from collections import defaultdict, Counter
from urllib.parse import urljoin

# autMan 全局对象声明
try:
    import middleware
    sender = middleware.Sender(middleware.getSenderID())
except ImportError:
    class MockSender:
        def getMessage(self): 
            return """汇总群聊
[2025-01-21 09:30] 张三: 大家好，今天的会议改到下午3点
[2025-01-21 09:31] 李四: 收到，我会准时参加
[2025-01-21 09:32] 王五: 好的，有什么需要准备的吗？
[2025-01-21 09:35] 张三: 需要准备上个月的销售报表
[2025-01-21 09:40] 赵六: 我这边的数据已经整理好了
[2025-01-21 10:15] 李四: @张三 会议室定好了吗？
[2025-01-21 10:16] 张三: 已经定好了，会议室A
[2025-01-21 10:20] 王五: 那我们下午见
[2025-01-21 11:30] 钱七: 刚看到消息，我也会参加
[2025-01-21 14:50] 张三: 大家可以来会议室了
[2025-01-21 15:00] 李四: 到了
[2025-01-21 15:02] 王五: 马上到"""
        def reply(self, msg): print(f"回复: {msg}")
    sender = MockSender()

def printf(msg):
    """日志输出"""
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    print(f"[{timestamp}] {msg}")

def fetch_log_from_url(log_url, hours_back=24):
    """从日志服务器获取群聊消息"""
    try:
        printf(f"🌐 尝试从日志服务器获取消息: {log_url}")
        
        # 构建日志文件URL - 根据常见的日志服务器结构
        possible_paths = [
            "/api/logs/recent",
            "/logs/today",
            "/runLog/recent",
            "/log/runLog",
            "/api/runlog",
            "/logs"
        ]
        
        session = requests.Session()
        session.timeout = 10
        
        for path in possible_paths:
            try:
                url = urljoin(log_url.rstrip('/') + '/', path.lstrip('/'))
                printf(f"🔍 尝试访问: {url}")
                
                response = session.get(url)
                if response.status_code == 200:
                    content = response.text
                    if len(content) > 100 and ('群聊' in content or '消息' in content or ':' in content):
                        printf(f"✅ 成功获取日志内容: {len(content)}字符")
                        return content
                        
            except Exception as e:
                printf(f"⚠️ 访问 {url} 失败: {e}")
                continue
        
        # 如果API路径都失败，尝试直接访问根路径
        try:
            response = session.get(log_url)
            if response.status_code == 200:
                content = response.text
                if len(content) > 100:
                    printf(f"✅ 从根路径获取内容: {len(content)}字符")
                    return content
        except Exception as e:
            printf(f"⚠️ 根路径访问失败: {e}")
        
        printf(f"❌ 无法从日志服务器获取内容")
        return None
        
    except Exception as e:
        printf(f"❌ 日志获取异常: {e}")
        return None

def extract_chat_from_log(log_content):
    """从日志内容中提取群聊消息"""
    try:
        printf(f"🔍 从日志中提取群聊消息...")
        
        # 常见的微信框架日志格式
        log_patterns = [
            # 格式1: [时间][群聊消息] 群名 | 用户名: 消息内容
            re.compile(r'\[([^\]]+)\]\[群聊消息\]\s*([^|]+)\|\s*([^:：]+)[：:]\s*(.+)', re.IGNORECASE),
            # 格式2: [时间] 群聊消息 群名 用户名: 消息内容  
            re.compile(r'\[([^\]]+)\]\s*群聊消息\s+([^\s]+)\s+([^:：]+)[：:]\s*(.+)', re.IGNORECASE),
            # 格式3: 时间 [群聊] 群名 用户名: 消息内容
            re.compile(r'(\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2})\s*\[群聊\]\s*([^\s]+)\s+([^:：]+)[：:]\s*(.+)', re.IGNORECASE),
            # 格式4: [时间] 用户名(群名): 消息内容
            re.compile(r'\[([^\]]+)\]\s*([^(]+)\(([^)]+)\)[：:]\s*(.+)', re.IGNORECASE),
            # 格式5: 时间 群名-用户名: 消息内容
            re.compile(r'(\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2})\s*([^-]+)-([^:：]+)[：:]\s*(.+)', re.IGNORECASE),
        ]
        
        messages = []
        lines = log_content.split('\n')
        
        for line in lines:
            line = line.strip()
            if not line or len(line) < 10:
                continue
                
            for pattern in log_patterns:
                match = pattern.search(line)
                if match:
                    groups = match.groups()
                    if len(groups) >= 3:
                        time_str = groups[0]
                        username = groups[-2].strip()  # 倒数第二个是用户名
                        content = groups[-1].strip()   # 最后一个是消息内容
                        
                        # 解析时间
                        try:
                            if re.match(r'\d{4}-\d{2}-\d{2}', time_str):
                                msg_time = datetime.strptime(time_str[:19], '%Y-%m-%d %H:%M:%S')
                            else:
                                msg_time = datetime.now()
                        except:
                            msg_time = datetime.now()
                        
                        if username and content and len(content) > 1:
                            messages.append({
                                'time': msg_time,
                                'username': username,
                                'content': content
                            })
                            break
        
        printf(f"✅ 从日志提取到 {len(messages)} 条群聊消息")
        return messages
        
    except Exception as e:
        printf(f"❌ 日志提取异常: {e}")
        return []

def parse_chat_messages(text):
    """解析群聊消息"""
    try:
        printf(f"🔍 开始解析群聊消息...")
        printf(f"📝 输入文本长度: {len(text)}字符")
        
        messages = []
        lines = text.strip().split('\n')
        printf(f"📄 分割后行数: {len(lines)}行")
        
        # 支持更多消息格式
        patterns = [
            # 格式1: [2025-01-21 09:30] 张三: 消息内容
            re.compile(r'^\[(\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}(?::\d{2})?)\]\s*([^:：]+)[：:]\s*(.+)$'),
            # 格式2: [09:30] 张三: 消息内容
            re.compile(r'^\[(\d{2}:\d{2}(?::\d{2})?)\]\s*([^:：]+)[：:]\s*(.+)$'),
            # 格式3: 2025-01-21 09:30 张三: 消息内容
            re.compile(r'^(\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}(?::\d{2})?)\s+([^:：]+)[：:]\s*(.+)$'),
            # 格式4: 09:30 张三: 消息内容
            re.compile(r'^(\d{2}:\d{2}(?::\d{2})?)\s+([^:：]+)[：:]\s*(.+)$'),
            # 格式5: 张三: 消息内容 (无时间)
            re.compile(r'^([^:：\[\]\d\s]+)[：:]\s*(.+)$'),
            # 格式6: 张三 说: 消息内容
            re.compile(r'^([^:：\[\]\d\s]+)\s*说[：:]\s*(.+)$'),
            # 格式7: 任意时间格式 + 用户名: 消息
            re.compile(r'^(.+?)\s+([^:：\[\]\d\s]+)[：:]\s*(.+)$'),
            # 格式8: 微信群聊导出格式 - 用户名 时间\n消息内容
            re.compile(r'^([^0-9\[\]]+)\s+(\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2})$'),
        ]
        
        current_time = datetime.now()
        pending_message = None  # 用于处理分行消息
        
        for i, line in enumerate(lines):
            line = line.strip()
            if not line:
                continue
                
            # 跳过触发词
            if line in ['汇总群聊', '群聊汇总', '分析群聊', '群聊分析', '获取群聊']:
                printf(f"⏭️ 跳过触发词: {line}")
                continue
            
            printf(f"🔍 处理第{i+1}行: {line[:50]}...")
            
            # 如果有待处理的消息头，检查当前行是否是消息内容
            if pending_message and not any(char in line for char in [':', '：', '[', ']']) and len(line) > 5:
                # 当前行可能是上一行用户名对应的消息内容
                pending_message['content'] = line
                messages.append(pending_message)
                printf(f"✅ 成功解析分行消息: {pending_message['username']} -> {line[:20]}...")
                pending_message = None
                continue
            
            parsed = False
            for j, pattern in enumerate(patterns):
                match = pattern.match(line)
                if match:
                    groups = match.groups()
                    printf(f"✅ 匹配模式{j+1}, 组数: {len(groups)}")
                    
                    if len(groups) == 3:  # 有时间戳
                        time_str, username, content = groups
                        
                        # 解析时间
                        msg_time = current_time
                        try:
                            if re.match(r'^\d{4}-\d{2}-\d{2}', time_str):
                                # 完整日期时间
                                if len(time_str.split()) == 2:
                                    msg_time = datetime.strptime(time_str, '%Y-%m-%d %H:%M')
                                else:
                                    msg_time = datetime.strptime(time_str, '%Y-%m-%d %H:%M:%S')
                            elif re.match(r'^\d{2}:\d{2}', time_str):
                                # 只有时间，使用今天的日期
                                today = datetime.now().date()
                                time_part = datetime.strptime(time_str, '%H:%M').time()
                                msg_time = datetime.combine(today, time_part)
                        except Exception as time_error:
                            printf(f"⚠️ 时间解析失败: {time_error}, 使用当前时间")
                            msg_time = current_time
                            
                    elif len(groups) == 2:  # 可能是用户名+时间，或用户名+内容
                        if re.match(r'.*\d{4}-\d{2}-\d{2}', groups[1]):
                            # 用户名 + 时间格式，消息内容在下一行
                            username = groups[0].strip()
                            time_str = groups[1].strip()
                            try:
                                msg_time = datetime.strptime(time_str, '%Y-%m-%d %H:%M:%S')
                            except:
                                msg_time = current_time
                            pending_message = {
                                'time': msg_time,
                                'username': username,
                                'content': ''
                            }
                            parsed = True
                            break
                        else:
                            # 用户名 + 内容格式
                            username, content = groups
                            msg_time = current_time
                    else:
                        continue
                    
                    # 清理用户名和内容
                    username = username.strip()
                    if 'content' in locals():
                        content = content.strip()
                        
                        if username and content and len(content) > 0:
                            messages.append({
                                'time': msg_time,
                                'username': username,
                                'content': content
                            })
                            printf(f"✅ 成功解析: {username} -> {content[:20]}...")
                            parsed = True
                            break
            
            # 如果所有模式都不匹配，但内容看起来像消息
            if not parsed and not pending_message and len(line) > 3:
                # 检查是否包含常见的消息特征
                if any(char in line for char in [':', '：', '说', '讲', '问', '答', '@']):
                    printf(f"🔄 作为匿名消息处理: {line[:30]}...")
                    messages.append({
                        'time': current_time,
                        'username': '匿名用户',
                        'content': line
                    })
                else:
                    printf(f"⏭️ 跳过非消息行: {line[:30]}...")
        
        printf(f"✅ 解析完成，共{len(messages)}条消息")
        
        # 打印前几条消息用于调试
        for i, msg in enumerate(messages[:3]):
            printf(f"📋 消息{i+1}: {msg['username']} -> {msg['content'][:30]}...")
        
        return messages
        
    except Exception as e:
        printf(f"❌ 消息解析异常: {e}")
        return []

def analyze_chat_statistics(messages):
    """分析群聊统计信息"""
    try:
        if not messages:
            return {}
            
        # 用户活跃度统计
        user_stats = defaultdict(int)
        user_content_length = defaultdict(int)
        
        # 时间分布统计
        hour_stats = defaultdict(int)
        
        # 关键词统计
        all_content = ""
        
        for msg in messages:
            user_stats[msg['username']] += 1
            user_content_length[msg['username']] += len(msg['content'])
            hour_stats[msg['time'].hour] += 1
            all_content += msg['content'] + " "
        
        # 最活跃用户
        most_active_users = sorted(user_stats.items(), key=lambda x: x[1], reverse=True)[:5]
        
        # 最活跃时间段
        most_active_hours = sorted(hour_stats.items(), key=lambda x: x[1], reverse=True)[:3]
        
        # 平均消息长度
        avg_msg_length = sum(len(msg['content']) for msg in messages) / len(messages) if messages else 0
        
        # 时间跨度
        if len(messages) > 1:
            time_span = max(msg['time'] for msg in messages) - min(msg['time'] for msg in messages)
            time_span_hours = time_span.total_seconds() / 3600
        else:
            time_span_hours = 0
        
        return {
            'total_messages': len(messages),
            'total_users': len(user_stats),
            'most_active_users': most_active_users,
            'most_active_hours': most_active_hours,
            'avg_message_length': round(avg_msg_length, 1),
            'time_span_hours': round(time_span_hours, 1),
            'all_content': all_content.strip()
        }
        
    except Exception as e:
        printf(f"❌ 统计分析异常: {e}")
        return {}

def analyze_with_ai(messages, stats):
    """使用AI分析群聊内容"""
    try:
        printf(f"🤖 开始AI分析...")
        
        # 获取配置
        try:
            api_key = middleware.bucketGet("WXQUNLIAO", "api_key") or "sk-eqyHojXr9c5zuC9t38ISML0FRhvJAJilQOxSxL4tU3HDf7S8"
            base_url = middleware.bucketGet("WXQUNLIAO", "base_url") or "https://tbai.xin/v1"
            model = middleware.bucketGet("WXQUNLIAO", "model") or "gemini-2.5-pro"
        except:
            api_key = "sk-eqyHojXr9c5zuC9t38ISML0FRhvJAJilQOxSxL4tU3HDf7S8"
            base_url = "https://tbai.xin/v1"
            model = "gemini-2.5-pro"
        
        # 构建消息历史文本
        chat_history = ""
        for msg in messages[-50:]:  # 只取最近50条消息避免token过多
            time_str = msg['time'].strftime('%H:%M')
            chat_history += f"[{time_str}] {msg['username']}: {msg['content']}\n"
        
        # 构建分析prompt
        prompt = f"""请分析以下微信群聊记录并返回JSON格式的汇总报告：

群聊统计信息：
- 总消息数：{stats.get('total_messages', 0)}
- 参与人数：{stats.get('total_users', 0)}
- 时间跨度：{stats.get('time_span_hours', 0)}小时
- 平均消息长度：{stats.get('avg_message_length', 0)}字符

群聊记录：
{chat_history[:2000]}

请返回以下JSON格式：
{{
    "summary": "群聊内容概述(100字内)",
    "main_topics": ["主要话题1", "主要话题2", "主要话题3"],
    "key_discussions": [
        {{"topic": "讨论主题", "participants": ["参与者1", "参与者2"], "summary": "讨论要点"}},
        {{"topic": "讨论主题", "participants": ["参与者1", "参与者2"], "summary": "讨论要点"}}
    ],
    "activity_analysis": {{
        "engagement_level": "高/中/低",
        "interaction_quality": "互动质量评价",
        "discussion_depth": "讨论深度评价"
    }},
    "insights": [
        "洞察1：群聊氛围或趋势分析",
        "洞察2：重要决策或结论",
        "洞察3：需要关注的问题"
    ],
    "action_items": [
        "待办事项1",
        "待办事项2"
    ],
    "tags": ["标签1", "标签2", "标签3"]
}}"""

        # API调用
        for attempt in range(2):
            try:
                if attempt > 0:
                    printf(f"⏳ 第{attempt + 1}次尝试...")
                    time.sleep(1)

                headers = {
                    'Authorization': f'Bearer {api_key}',
                    'Content-Type': 'application/json'
                }

                data = {
                    'model': model,
                    'messages': [{'role': 'user', 'content': prompt}],
                    'temperature': 0.3,
                    'max_tokens': 1200,
                    'top_p': 0.9,
                    'stream': False
                }

                response = requests.post(f"{base_url}/chat/completions", headers=headers, json=data, timeout=25)
                
                if response.status_code == 200:
                    result = response.json()
                    if 'choices' in result and result['choices']:
                        content = result['choices'][0]['message']['content']
                        return parse_ai_response(content)
                elif response.status_code == 429:
                    printf(f"⚠️ API速率限制，重试中...")
                    continue
                else:
                    printf(f"❌ API调用失败: {response.status_code}")
                    
            except Exception as e:
                printf(f"⚠️ API调用异常: {e}")
                continue
        
        printf(f"❌ AI分析失败")
        return None
        
    except Exception as e:
        printf(f"❌ AI分析异常: {e}")
        return None

def parse_ai_response(content):
    """解析AI响应"""
    try:
        # 提取JSON内容
        json_patterns = [
            re.compile(r'\{[^{}]*"summary"[^{}]*\}', re.DOTALL),
            re.compile(r'```json\s*(.*?)\s*```', re.DOTALL),
            re.compile(r'\{.*?\}', re.DOTALL)
        ]

        json_str = None
        for pattern in json_patterns:
            match = pattern.search(content)
            if match:
                json_str = match.group(1) if 'json' in pattern.pattern else match.group(0)
                break

        if not json_str:
            return None

        # 解析JSON
        try:
            return json.loads(json_str)
        except json.JSONDecodeError:
            # 修复常见JSON问题
            json_str = re.sub(r',(\s*[}\]])', r'\1', json_str)
            json_str = re.sub(r'([{,]\s*)(\w+):', r'\1"\2":', json_str)
            try:
                return json.loads(json_str)
            except:
                return None

    except Exception as e:
        printf(f"❌ 响应解析失败: {e}")
        return None

def format_chat_analysis(analysis, stats):
    """格式化群聊分析结果"""
    try:
        parts = []

        # 基础统计信息
        stats_section = [
            f"📊 群聊统计",
            f"• 消息总数：{stats.get('total_messages', 0)}条",
            f"• 参与人数：{stats.get('total_users', 0)}人", 
            f"• 时间跨度：{stats.get('time_span_hours', 0)}小时",
            f"• 平均消息长度：{stats.get('avg_message_length', 0)}字符"
        ]
        
        # 最活跃用户
        if stats.get('most_active_users'):
            stats_section.append("• 最活跃用户：")
            for i, (user, count) in enumerate(stats['most_active_users'][:3], 1):
                stats_section.append(f"  {i}. {user} ({count}条)")
        
        parts.append('\n'.join(stats_section))

        # 内容概述
        if 'summary' in analysis:
            parts.append(f"📝 内容概述\n{analysis['summary']}")

        # 主要话题
        if 'main_topics' in analysis and analysis['main_topics']:
            topics_section = ["🎯 主要话题"]
            for i, topic in enumerate(analysis['main_topics'], 1):
                topics_section.append(f"{i}. {topic}")
            parts.append('\n'.join(topics_section))

        # 关键讨论
        if 'key_discussions' in analysis and analysis['key_discussions']:
            discussions_section = ["💬 关键讨论"]
            for i, discussion in enumerate(analysis['key_discussions'], 1):
                topic = discussion.get('topic', f'讨论{i}')
                participants = ', '.join(discussion.get('participants', []))
                summary = discussion.get('summary', '')
                discussions_section.append(f"{i}. {topic}")
                if participants:
                    discussions_section.append(f"   参与者：{participants}")
                if summary:
                    discussions_section.append(f"   要点：{summary}")
            parts.append('\n'.join(discussions_section))

        # 活跃度分析
        if 'activity_analysis' in analysis:
            activity = analysis['activity_analysis']
            if isinstance(activity, dict):
                activity_section = ["📈 活跃度分析"]
                
                engagement = activity.get('engagement_level', '')
                if engagement:
                    activity_section.append(f"• 参与度：{engagement}")
                
                quality = activity.get('interaction_quality', '')
                if quality:
                    activity_section.append(f"• 互动质量：{quality}")
                
                depth = activity.get('discussion_depth', '')
                if depth:
                    activity_section.append(f"• 讨论深度：{depth}")
                
                if len(activity_section) > 1:
                    parts.append('\n'.join(activity_section))

        # 重要洞察
        if 'insights' in analysis and analysis['insights']:
            insights_section = ["🔍 重要洞察"]
            for i, insight in enumerate(analysis['insights'], 1):
                insights_section.append(f"{i}. {insight}")
            parts.append('\n'.join(insights_section))

        # 待办事项
        if 'action_items' in analysis and analysis['action_items']:
            actions_section = ["✅ 待办事项"]
            for i, action in enumerate(analysis['action_items'], 1):
                actions_section.append(f"{i}. {action}")
            parts.append('\n'.join(actions_section))

        # 标签
        if 'tags' in analysis and analysis['tags']:
            tags = ' '.join([f"#{tag}" for tag in analysis['tags']])
            parts.append(f"🏷️ 相关标签\n{tags}")

        return '\n' + '='*40 + '\n'.join([''] + parts + ['']) + '='*40

    except Exception as e:
        printf(f"❌ 格式化失败: {e}")
        return str(analysis)

def main():
    """主函数"""
    try:
        user_input = sender.getMessage()
        printf(f"🚀 收到输入: {user_input[:100]}...")

        # 检查是否是群聊汇总请求
        trigger_keywords = ['汇总群聊', '群聊汇总', '分析群聊', '群聊分析', '获取群聊']
        if not any(keyword in user_input for keyword in trigger_keywords):
            return  # 静默退出

        printf(f"✅ 检测到群聊汇总请求")

        # 获取配置参数
        try:
            min_messages = int(middleware.bucketGet("QUNLIAO", "min_messages") or "10")
            max_messages = int(middleware.bucketGet("QUNLIAO", "max_messages") or "100")
            log_url = middleware.bucketGet("QUNLIAO", "log_url") or ""
        except:
            min_messages = 10
            max_messages = 100
            log_url = ""

        messages = []
        
        # 1. 首先尝试从用户输入解析消息
        if len(user_input.split('\n')) > 2:
            printf(f"📝 尝试从用户输入解析消息...")
            messages = parse_chat_messages(user_input)
        
        # 2. 如果用户输入没有足够消息，且配置了日志URL，尝试从日志获取
        if len(messages) < min_messages and log_url:
            printf(f"🌐 消息数量不足，尝试从日志服务器获取...")
            log_content = fetch_log_from_url(log_url)
            if log_content:
                log_messages = extract_chat_from_log(log_content)
                if log_messages:
                    messages.extend(log_messages)
                    printf(f"✅ 从日志获取到额外 {len(log_messages)} 条消息")
        
        # 3. 如果还是没有足够消息，提供详细的帮助信息
        if not messages:
            help_msg = f"""❌ 未能解析到有效的群聊消息

🔧 获取群聊消息的几种方式：

1️⃣ **直接粘贴群聊记录**
📋 支持的消息格式示例：
• [2025-01-21 09:30] 张三: 大家好
• [09:30] 张三: 大家好  
• 2025-01-21 09:30 张三: 大家好
• 09:30 张三: 大家好
• 张三: 大家好
• 张三 说: 大家好

2️⃣ **配置日志服务器自动获取**
💡 在插件配置中设置"日志服务器URL"参数
例如：http://192.168.50.43:12138

3️⃣ **从微信群聊导出**
📱 使用微信PC版或第三方工具导出群聊记录

💡 请确保每条消息包含用户名和内容，用冒号(:)或中文冒号(：)分隔"""
            
            sender.reply(help_msg)
            return

        if len(messages) < min_messages:
            sender.reply(f"⚠️ 消息数量太少({len(messages)}条)，建议至少{min_messages}条消息才能进行有效分析\n\n💡 可以尝试配置日志服务器URL自动获取更多消息")
            return

        # 限制消息数量并按时间排序
        messages = sorted(messages, key=lambda x: x['time'])
        if len(messages) > max_messages:
            messages = messages[-max_messages:]
            printf(f"📝 消息数量过多，只分析最近{max_messages}条")

        printf(f"✅ 消息解析成功: {len(messages)}条消息")

        # 统计分析
        stats = analyze_chat_statistics(messages)
        printf(f"✅ 统计分析完成")

        # AI分析
        analysis = analyze_with_ai(messages, stats)
        if not analysis:
            sender.reply("❌ AI分析失败，请稍后重试")
            return

        printf(f"✅ AI分析成功")

        # 格式化并返回结果
        formatted_result = format_chat_analysis(analysis, stats)
        sender.reply(formatted_result)
        printf(f"✅ 群聊汇总完成")

    except Exception as e:
        printf(f"❌ 插件异常: {e}")
        sender.reply("❌ 插件执行异常，请稍后重试")

if __name__ == "__main__":
    main()