#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
聚算云登录并修改用户等级脚本
包含自动登录获取token功能
"""

import requests
import json
import time
from datetime import datetime
import re

class JusuanyunManager:
    def __init__(self, base_url="https://cdn.jusuanyun.vip"):
        self.base_url = base_url
        self.session = requests.Session()
        self.token = ""
        self.user_info = {}
        
        # 设置通用请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 18_3_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3.1 Mobile/15E148 Safari/604.1',
            'Accept': '*/*',
            'Accept-Language': 'zh-CN,zh-Hans;q=0.9',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Referer': f'{base_url}/'
        })
    
    def printf(self, msg):
        """日志输出"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        print(f"[{timestamp}] {msg}")
    
    def login(self, username, password):
        """用户登录"""
        try:
            self.printf(f"🔐 开始登录: {username}")
            
            # 可能的登录API端点
            login_endpoints = [
                "/api/user/login",
                "/api/auth/login", 
                "/api/login",
                "/login",
                "/user/login"
            ]
            
            login_data = {
                'username': username,
                'password': password,
                'phone': username,
                'mobile': username
            }
            
            # 尝试不同的登录端点和参数组合
            for endpoint in login_endpoints:
                url = f"{self.base_url}{endpoint}"
                self.printf(f"🔄 尝试登录端点: {endpoint}")
                
                # 尝试不同的参数组合
                data_combinations = [
                    {'phone': username, 'password': password},
                    {'mobile': username, 'password': password},
                    {'username': username, 'password': password},
                    {'account': username, 'password': password},
                    {'loginName': username, 'loginPwd': password}
                ]
                
                for data in data_combinations:
                    try:
                        # 尝试POST请求
                        self.session.headers['Content-Type'] = 'application/x-www-form-urlencoded'
                        response = self.session.post(url, data=data, timeout=30)
                        
                        if response.status_code == 200:
                            try:
                                result = response.json()
                                self.printf(f"📥 登录响应: {result}")
                                
                                if result.get('code') == 0 or result.get('success') == True:
                                    # 登录成功，提取token
                                    token = result.get('token') or result.get('data', {}).get('token')
                                    if token:
                                        self.token = token
                                        self.session.headers['token'] = token
                                        self.user_info = result.get('data', {})
                                        self.printf(f"✅ 登录成功！Token: {token[:20]}...")
                                        return True
                                else:
                                    self.printf(f"❌ 登录失败: {result.get('msg', '未知错误')}")
                            except json.JSONDecodeError:
                                self.printf(f"❌ 响应不是有效JSON: {response.text[:100]}")
                        else:
                            self.printf(f"❌ HTTP错误: {response.status_code}")
                            
                    except Exception as e:
                        self.printf(f"❌ 请求异常: {e}")
                        continue
                
                time.sleep(1)  # 避免请求过快
            
            self.printf("❌ 所有登录尝试都失败了")
            return False
            
        except Exception as e:
            self.printf(f"❌ 登录异常: {e}")
            return False
    
    def get_team_info(self):
        """获取团队信息"""
        try:
            url = f"{self.base_url}/api/user/getTeamInfo"
            self.printf(f"🔍 获取团队信息...")
            
            response = self.session.get(url, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                self.printf(f"📊 团队信息响应: {data}")
                
                if data.get('code') == 0:
                    self.printf(f"✅ 获取团队信息成功")
                    return data.get('data', {})
                else:
                    self.printf(f"❌ 获取团队信息失败: {data.get('msg')}")
            else:
                self.printf(f"❌ HTTP错误: {response.status_code}")
                
            return None
            
        except Exception as e:
            self.printf(f"❌ 获取团队信息异常: {e}")
            return None
    
    def discover_api_endpoints(self):
        """探测可用的API端点"""
        try:
            self.printf("🔍 开始探测API端点...")
            
            # 常见的API路径模式
            common_patterns = [
                "/api/user/updateLevel",
                "/api/user/updateUserLevel",
                "/api/user/setLevel",
                "/api/user/upgradeLevel",
                "/api/user/modifyLevel",
                "/api/admin/updateUserLevel",
                "/api/admin/setUserLevel",
                "/api/admin/modifyLevel",
                "/api/level/update",
                "/api/level/set",
                "/api/member/updateLevel",
                "/api/member/setLevel"
            ]
            
            available_endpoints = []
            
            for endpoint in common_patterns:
                url = f"{self.base_url}{endpoint}"
                try:
                    # 发送OPTIONS请求检查端点是否存在
                    response = self.session.options(url, timeout=10)
                    if response.status_code != 404:
                        available_endpoints.append(endpoint)
                        self.printf(f"✅ 发现可用端点: {endpoint}")
                    
                    # 也尝试POST请求看是否有不同的响应
                    response = self.session.post(url, data={}, timeout=10)
                    if response.status_code not in [404, 405] and endpoint not in available_endpoints:
                        available_endpoints.append(endpoint)
                        self.printf(f"✅ 发现可用端点: {endpoint}")
                        
                except:
                    pass
                
                time.sleep(0.5)
            
            if available_endpoints:
                self.printf(f"🎯 发现 {len(available_endpoints)} 个可用端点")
                return available_endpoints
            else:
                self.printf("❌ 未发现可用的更新端点")
                return []
                
        except Exception as e:
            self.printf(f"❌ 探测API端点异常: {e}")
            return []
    
    def update_user_level(self, user_id, new_level, endpoints=None):
        """更新用户等级"""
        try:
            if not endpoints:
                endpoints = [
                    "/api/user/updateLevel",
                    "/api/user/updateUserLevel",
                    "/api/admin/updateUserLevel",
                    "/api/user/setLevel"
                ]
            
            self.printf(f"🔄 尝试更新用户等级: {user_id} -> 等级 {new_level}")
            
            for endpoint in endpoints:
                url = f"{self.base_url}{endpoint}"
                
                # 尝试不同的参数格式
                data_formats = [
                    {'userId': user_id, 'levelId': new_level},
                    {'user_id': user_id, 'level_id': new_level},
                    {'id': user_id, 'level': new_level},
                    {'userId': str(user_id), 'levelId': str(new_level)},
                    {'memberId': user_id, 'memberLevel': new_level},
                    {'uid': user_id, 'level': new_level}
                ]
                
                for data in data_formats:
                    try:
                        self.printf(f"🔄 尝试: {endpoint} 参数: {data}")
                        
                        response = self.session.post(url, data=data, timeout=30)
                        
                        if response.status_code == 200:
                            try:
                                result = response.json()
                                self.printf(f"📥 更新响应: {result}")
                                
                                if result.get('code') == 0:
                                    self.printf(f"✅ 等级更新成功！")
                                    return result
                                elif result.get('code') == -2:
                                    self.printf(f"🔑 Token错误，需要重新登录")
                                    return None
                                else:
                                    self.printf(f"❌ 更新失败: {result.get('msg', '未知错误')}")
                            except json.JSONDecodeError:
                                self.printf(f"❌ 响应不是有效JSON")
                        else:
                            self.printf(f"❌ HTTP错误: {response.status_code}")
                            
                    except Exception as e:
                        self.printf(f"❌ 请求异常: {e}")
                        continue
                    
                    time.sleep(1)
            
            return None
            
        except Exception as e:
            self.printf(f"❌ 更新用户等级异常: {e}")
            return None

def main():
    """主函数"""
    print("=" * 60)
    print("🚀 聚算云用户等级修改工具 (含自动登录)")
    print("=" * 60)
    
    # 登录信息
    username = "13751504455"
    password = "Leo030340"
    target_level = 3  # 目标等级
    
    # 创建管理器实例
    manager = JusuanyunManager()
    
    # 1. 登录获取token
    manager.printf("🔐 步骤1: 用户登录")
    if not manager.login(username, password):
        manager.printf("❌ 登录失败，无法继续")
        return
    
    # 2. 获取当前用户信息
    manager.printf("🔍 步骤2: 获取用户信息")
    team_info = manager.get_team_info()
    
    if team_info:
        user_id = team_info.get('userId')
        current_level = team_info.get('levelId')
        phone = team_info.get('phone')
        team_income = team_info.get('teamIncome')
        
        manager.printf(f"📊 当前用户信息:")
        manager.printf(f"   用户ID: {user_id}")
        manager.printf(f"   当前等级: {current_level}")
        manager.printf(f"   手机号: {phone}")
        manager.printf(f"   团队收益: {team_income}")
        
        if current_level == target_level:
            manager.printf(f"⚠️  用户已经是目标等级 {target_level}")
            return
    else:
        manager.printf("❌ 无法获取用户信息")
        return
    
    # 3. 探测可用API端点
    manager.printf("🔍 步骤3: 探测可用API端点")
    available_endpoints = manager.discover_api_endpoints()
    
    # 4. 尝试更新用户等级
    manager.printf(f"🔄 步骤4: 更新用户等级 {current_level} -> {target_level}")
    
    result = manager.update_user_level(user_id, target_level, available_endpoints)
    
    if result:
        manager.printf("🎉 等级更新成功！")
        manager.printf(f"📊 更新结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
        
        # 5. 验证更新结果
        manager.printf("🔍 步骤5: 验证更新结果")
        time.sleep(3)
        
        updated_info = manager.get_team_info()
        if updated_info:
            new_level = updated_info.get('levelId')
            if new_level == target_level:
                manager.printf(f"✅ 验证成功！等级已更新为: {new_level}")
            else:
                manager.printf(f"⚠️  等级未变更，当前仍为: {new_level}")
        else:
            manager.printf("❌ 无法验证更新结果")
    else:
        manager.printf("❌ 等级更新失败")
        manager.printf("💡 可能的原因:")
        manager.printf("   1. 用户权限不足")
        manager.printf("   2. API端点不存在或已变更")
        manager.printf("   3. 业绩未达到升级要求")
        manager.printf("   4. 系统不允许手动修改等级")
    
    print("=" * 60)
    print("🏁 脚本执行完成")
    print("=" * 60)

if __name__ == "__main__":
    main()